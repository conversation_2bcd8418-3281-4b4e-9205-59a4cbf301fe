#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

main() {

    has_cmd virt-install || fail "virt-install is not installed. Please install libvirt and virt-install first."

    slog "Creating 4 libvirt VMs (Ubuntu, Fedora, Arch, Debian)..."

    slog "Creating Ubuntu VM: ubuntu-vm"
    vm-create --distro ubuntu --name ubuntu-vm

    slog "Creating Fedora VM: fedora-vm"
    vm-create --distro fedora --name fedora-vm

    slog "Creating Arch VM: arch-vm"
    vm-create --distro arch --name arch-vm

    slog "Creating Debian VM: debian-vm"
    vm-create --distro debian --name debian-vm

    slog "Listing created VMs:"
    virsh list --all

    success "All VMs created successfully!"
    slog "You can access them using: virsh console <vm-name>"
}

main "$@"
